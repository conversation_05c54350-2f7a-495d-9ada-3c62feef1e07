{"os": "Linux-5.4.0-216-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.19", "startedAt": "2025-05-29T09:41:30.221707Z", "args": ["--data_dir", "/data/datasets/processed_datasets/amazon", "--model_save_dir", "/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_analysis_20250529_094125/temp_0.1", "--batch_size", "512", "--num_epochs", "1", "--learning_rate", "1e-3", "--device", "cuda", "--temperature", "0.1", "--wandb_project", "GraphLLM4CTR_Hyperparameter_Temperature_Amazon_contrastive_FAST", "--dataset_type", "amazon"], "program": "contrastive_learning_train_for_check.py", "codePath": "models/contrastive_learning_train_for_check.py", "git": {"remote": "**************:gaoshan-code/GraphLLM4CTR.git", "commit": "467a19fe01d8d844b5278c0048791f6c55aab396"}, "email": "<EMAIL>", "root": "/root/code/GraphLLM4CTR/models", "host": "vmInstancekdtfa3ig", "executable": "/root/miniconda3/envs/py38/bin/python", "codePathLocal": "contrastive_learning_train_for_check.py", "cpu_count": 16, "cpu_count_logical": 16, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 1, "disk": {"/": {"total": "51835101184", "used": "42501640192"}}, "memory": {"total": "120219561984"}, "cpu": {"count": 16, "countLogical": 16}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.6"}