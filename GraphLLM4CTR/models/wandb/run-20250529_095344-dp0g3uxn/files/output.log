Some weights of RobertaModel were not initialized from the model checkpoint at /root/code/roberta-base and are newly initialized: ['roberta.pooler.dense.bias', 'roberta.pooler.dense.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
/root/code/GraphLLM4CTR/models/embedding_loader.py:54: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  state = torch.load(qformer_checkpoint_path, map_location=device)
Traceback (most recent call last):
  File "hyperparameter_analysis_embedding.py", line 565, in <module>
    results = run_embedding_size_analysis(args)
  File "hyperparameter_analysis_embedding.py", line 428, in run_embedding_size_analysis
    results = train_and_evaluate(
  File "hyperparameter_analysis_embedding.py", line 152, in train_and_evaluate
    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 54, in __init__
    state = torch.load(qformer_checkpoint_path, map_location=device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1065, in load
    with _open_file_like(f, 'rb') as opened_file:
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 468, in _open_file_like
    return _open_file(name_or_buffer, mode)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 449, in __init__
    super().__init__(open(name, mode))
FileNotFoundError: [Errno 2] No such file or directory: '/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.7/qformer_epoch1.pt'
Traceback (most recent call last):
  File "hyperparameter_analysis_embedding.py", line 565, in <module>
    results = run_embedding_size_analysis(args)
  File "hyperparameter_analysis_embedding.py", line 428, in run_embedding_size_analysis
    results = train_and_evaluate(
  File "hyperparameter_analysis_embedding.py", line 152, in train_and_evaluate
    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)
  File "/root/code/GraphLLM4CTR/models/embedding_loader.py", line 54, in __init__
    state = torch.load(qformer_checkpoint_path, map_location=device)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 1065, in load
    with _open_file_like(f, 'rb') as opened_file:
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 468, in _open_file_like
    return _open_file(name_or_buffer, mode)
  File "/root/miniconda3/envs/py38/lib/python3.8/site-packages/torch/serialization.py", line 449, in __init__
    super().__init__(open(name, mode))
FileNotFoundError: [Errno 2] No such file or directory: '/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.7/qformer_epoch1.pt'
