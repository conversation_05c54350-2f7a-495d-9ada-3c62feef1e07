requests==2.32.3
scikit-learn==1.3.2
debugpy==1.8.5
plane==0.2.1
gitdb==4.0.12
tzdata==2025.2
cachetools==5.5.2
fqdn==1.5.1
comm==0.2.2
nvidia-pyindex==1.0.9
MarkupSafe==2.1.5
torch_sparse==0.6.18+pt24cu121
jsonschema-specifications==2023.12.1
babel==2.16.0
nvidia-cuda-nvrtc-cu12==12.1.105
colorama==0.4.4
recbole==1.2.1
google-pasta==0.2.0
ptyprocess==0.7.0
nest-asyncio==1.6.0
termcolor==2.4.0
pydantic==2.10.6
pynvml==11.5.3
nvidia-cufft-cu12==*********
async-timeout==5.0.1
referencing==0.35.1
tornado==6.4.1
Jinja2==3.1.4
setproctitle==1.3.6
nvidia-cufft-cu115==**********
multidict==6.1.0
nvidia-cusolver-cu12==**********
networkx==3.1
tensorboard==2.14.0
ipykernel==6.29.5
annotated-types==0.7.0
nvidia-cuda-runtime-cu115==11.5.117
jupyter_client==8.6.2
overrides==7.7.0
colorlog==4.7.2
Keras-Applications==1.0.8
contourpy==1.1.1
msgpack==1.1.0
threadpoolctl==3.5.0
charset-normalizer==3.3.2
yarl==1.15.2
tokenizers==0.20.3
Pygments==2.18.0
torch_cluster==1.6.3+pt24cu121
python-dateutil==2.9.0.post0
pandocfilters==1.5.1
protobuf==3.20.3
nvidia-dali-nvtf-plugin==1.8.0+nv21.12
setuptools==72.1.0
argon2-cffi==23.1.0
astor==0.8.1
plotly==6.0.1
pyasn1_modules==0.4.2
safetensors==0.5.3
pexpect==4.9.0
mistune==3.0.2
jupyter-console==6.6.3
google-auth==2.40.1
nvidia-cusparse-cu115==**********
wandb==0.19.10
pkgutil_resolve_name==1.3.10
jupyter==1.1.1
jupyterlab_pygments==0.3.0
huggingface-hub==0.30.2
tomli==2.0.1
pickleshare==0.7.5
jupyter-events==0.10.0
sympy==1.13.3
tinycss2==1.3.0
websocket-client==1.8.0
texttable==1.7.0
nvidia-cuda-nvcc-cu115==11.5.119
defusedxml==0.7.1
packaging==24.1
arrow==1.3.0
nvidia-cuda-cupti-cu12==12.1.105
ipywidgets==8.1.5
types-python-dateutil==2.9.0.20240906
scipy==1.10.1
ipython==8.12.3
parso==0.8.4
httpx==0.27.2
rsa==4.9.1
pillow==10.4.0
nvidia-cuda-cupti-cu115==11.5.114
importlib_metadata==8.5.0
fsspec==2025.3.0
smmap==5.0.2
nvidia-nccl-cu115==2.11.4
attrs==24.2.0
pyasn1==0.6.1
nvidia-curand-cu115==**********
jupyterlab_server==2.27.3
beautifulsoup4==4.12.3
jupyter_server_terminals==0.5.3
json5==0.9.25
importlib_resources==6.4.5
platformdirs==4.3.3
soupsieve==2.6
exceptiongroup==1.2.2
jsonpointer==3.0.0
aiohttp==3.10.11
joblib==1.4.2
widgetsnbextension==4.0.13
jupyterlab_widgets==3.0.13
certifi==2024.8.30
nvidia-cudnn-cu115==********
pydantic_core==2.27.2
nvidia-nccl-cu12==2.20.5
wcwidth==0.2.13
wrapt==1.16.0
asttokens==2.4.1
prometheus_client==0.20.0
nvidia-cuda-runtime-cu12==12.1.105
rfc3986-validator==0.1.1
ray==2.6.3
typing_extensions==4.12.2
torch==2.4.1
nvidia-curand-cu12==**********
mpmath==1.3.0
aiohappyeyeballs==2.4.4
Send2Trash==1.8.3
rpds-py==0.20.0
Markdown==3.7
webencodings==0.5.1
pandas==2.0.3
decorator==5.1.1
matplotlib==3.7.5
urllib3==2.2.3
click==8.1.8
thop==0.1.1-2209072238
tensorboard-data-server==0.7.2
argon2-cffi-bindings==21.2.0
triton==3.0.0
jupyter_core==5.7.2
executing==2.1.0
GPUtil==1.4.0
GitPython==3.1.44
Werkzeug==3.0.6
six==1.16.0
seaborn==0.13.2
anyio==4.4.0
aiosignal==1.3.1
PyYAML==6.0.2
jupyter_server==2.14.2
nbconvert==7.16.4
grpcio==1.66.1
bleach==6.1.0
opt-einsum==3.3.0
jupyterlab==4.2.5
docker-pycreds==0.4.0
tqdm==4.67.1
webcolors==24.8.0
notebook_shim==0.2.4
pip==24.2
cycler==0.12.1
idna==3.9
pure_eval==0.2.3
tabulate==0.9.0
transformers==4.46.3
nvidia-cublas-cu115==********
nvidia-nvtx-cu12==12.1.105
pyzmq==26.2.0
wheel==0.44.0
google-auth-oauthlib==1.0.0
torch_spline_conv==1.2.2+pt24cu121
jedi==0.19.1
filelock==3.16.1
psutil==6.0.0
frozenlist==1.5.0
cffi==1.17.1
python-json-logger==2.0.7
isoduration==20.11.0
backcall==0.2.0
fastjsonschema==2.20.0
numpy==1.23.5
sentencepiece==0.2.0
prompt_toolkit==3.0.47
zipp==3.20.2
nvidia-tensorflow==1.15.5+nv21.12
propcache==0.2.0
jupyterlab-language-pack-zh-CN==4.2.post3
matplotlib-inline==0.1.7
traitlets==5.14.3
nbformat==5.10.4
eval_type_backport==0.2.2
nvidia-cusparse-cu12==**********
kiwisolver==1.4.7
nvidia-cublas-cu12==********
rfc3339-validator==0.1.4
pytz==2024.2
jupyter-lsp==2.2.5
requests-oauthlib==2.0.0
nvidia-nvjitlink-cu12==12.9.41
torch-geometric==2.6.1
tensorflow-estimator==1.15.1
terminado==0.18.1
h5py==2.10.0
pycparser==2.22
async-lru==2.0.4
Keras-Preprocessing==1.1.2
nvidia-cusolver-cu115==**********
notebook==7.2.2
jsonschema==4.23.0
sniffio==1.3.1
sentry-sdk==2.27.0
torch_scatter==2.1.2+pt24cu121
nbclient==0.10.0
absl-py==2.1.0
stack-data==0.6.3
astunparse==1.6.3
httpcore==1.0.5
fonttools==4.57.0
nvidia-cudnn-cu12==********
uri-template==1.3.0
narwhals==1.38.1
gast==0.3.3
pyparsing==3.1.4
regex==2024.11.6
h11==0.14.0
oauthlib==3.2.2
nvidia-dali-cuda110==1.8.0
