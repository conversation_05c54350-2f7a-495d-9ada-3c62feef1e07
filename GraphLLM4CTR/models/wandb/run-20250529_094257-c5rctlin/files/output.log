2025-05-29 09:42:58,522 - __main__ - INFO - Loading dataset and dataloader
2025-05-29 09:43:01,265 - __main__ - INFO - Initializing batch builder

=== Initializing OptimizedBatchBuilder ===
Data directory: /data/datasets/processed_datasets/amazon
Dataset type: amazon
Batch size: 512
Device: cuda
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:158: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_user_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:160: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_item_item_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:162: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:164: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(user_user_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:166: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_click_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:168: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  torch.load(item_item_imp_graph_emb_path), freeze=False).to(self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:173: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_text_embeddings = torch.load(user_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:174: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_text_embeddings = torch.load(item_text_emb_path, map_location=self.device)
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.click_matrix = torch.load(f"{graph_matrix_dir}/click_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:63: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.impression_matrix = torch.load(f"{graph_matrix_dir}/impression_matrix.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:64: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1 = torch.load(f"{graph_matrix_dir}/user_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:65: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2 = torch.load(f"{graph_matrix_dir}/user_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:66: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1 = torch.load(f"{graph_matrix_dir}/item_edge_type1.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:67: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2 = torch.load(f"{graph_matrix_dir}/item_edge_type2.pt", map_location='cpu')
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:44: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:45: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.user_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/user_edge_type2.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:46: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type1_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type1.pt")
/root/code/GraphLLM4CTR/models/optimized_batch_builder_for_check.py:47: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  self.item_edge_type2_index = torch.load("/data/datasets/processed_datasets/amazon/graph_edge_matrix/item_edge_type2.pt")
2025-05-29 09:43:07,633 - __main__ - INFO - Initializing contrastive learning model
contrastive_learning_train_for_check.py:217: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
2025-05-29 09:43:08,033 - __main__ - INFO - Starting training
  0%|          | 0/1 [00:00<?, ?it/s]              contrastive_learning_train_for_check.py:256: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():        | 0/2124 [00:00<?, ?it/s]
Lambda values: λ1=1.0000, λ2=1.0000, λ3=1.0000, λ4=1.0000, λ5=1.0000
[Debug] Raw Grad Norm: 16.6848
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:09,983 - __main__ - INFO - Batch 0/2124, GPU Memory: 2.73GB
2025-05-29 09:43:09,983 - __main__ - INFO -   user_edge_type1: nnz=20
2025-05-29 09:43:09,984 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:09,984 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:09,984 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   0%|          | 8/2124 [00:06<21:35,  1.63it/s]  
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 14.5539
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 13.4356
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 16.1543
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 13.9433
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.2948
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.7339
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.8000, λ3=0.8000, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.7842
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.4141
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.5044
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.3666
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:15,750 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:15,750 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:15,750 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   1%|          | 18/2124 [00:11<19:22,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 12.2306
[Debug] Grad Norm: 0.9998 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.5721
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7999, λ3=0.7999, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.7514
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3515
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.3343
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.1479
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 11.2316
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7998, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.8122
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7997, λ3=0.7998, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.9214
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.7473
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:21,248 - __main__ - INFO -   user_edge_type2: nnz=19
2025-05-29 09:43:21,248 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:21,248 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   1%|▏         | 28/2124 [00:17<20:56,  1.67it/s]
Lambda values: λ1=0.8000, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.8110
[Debug] Grad Norm: 0.9999 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.7297
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7997, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.6690
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.6434
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.6467
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7997, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5495
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5565
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5458
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4758
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7996, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5240
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:26,927 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:26,928 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:26,928 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   2%|▏         | 38/2124 [00:22<19:17,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5119
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5254
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5090
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5103
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5092
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5058
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4402
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5132
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4842
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7996, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4807
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:32,474 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:32,474 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:32,474 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   2%|▏         | 48/2124 [00:28<19:09,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4672
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7995, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4707
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5081
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5080
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5068
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4789
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5102
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4867
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4510
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5130
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:38,209 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:38,209 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:38,209 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   3%|▎         | 58/2124 [00:34<19:51,  1.73it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4852
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5179
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5180
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4888
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5153
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5197
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5183
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5232
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5212
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5198
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:43,943 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:43,943 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:43,943 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   3%|▎         | 68/2124 [00:39<18:53,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5235
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5247
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5225
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5281
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5267
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5273
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5267
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5270
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4966
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5229
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:49,443 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:49,443 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:49,444 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   4%|▎         | 78/2124 [00:45<18:41,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5272
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4678
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5288
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5284
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5278
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5281
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5287
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5288
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5265
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5289
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:43:54,927 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:43:54,927 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:43:54,927 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   4%|▍         | 88/2124 [00:50<20:09,  1.68it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4968
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5299
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5289
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4986
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5276
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5291
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5279
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5307
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4684
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5009
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:00,646 - __main__ - INFO -   user_edge_type2: nnz=19
2025-05-29 09:44:00,646 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:00,646 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   5%|▍         | 98/2124 [00:56<18:40,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5302
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4989
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4659
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5299
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4983
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5303
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4997
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5302
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5476
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4703
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:06,383 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:06,383 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:06,383 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   5%|▌         | 108/2124 [01:02<18:48,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5346
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4961
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5311
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5331
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5309
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5328
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5023
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5342
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5332
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5344
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:11,932 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:11,932 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:11,932 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   6%|▌         | 118/2124 [01:07<18:31,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5335
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5014
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5345
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5027
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5363
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5366
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5337
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5368
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5044
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4737
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:17,473 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:17,473 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:17,473 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   6%|▌         | 128/2124 [01:13<19:01,  1.75it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5040
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5369
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5351
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5365
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4912
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5348
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5379
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5365
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5362
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4739
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:23,249 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:23,249 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:23,249 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   6%|▋         | 138/2124 [01:19<18:16,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5374
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5385
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5360
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5055
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5386
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5370
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5364
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5379
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5375
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:28,782 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:28,782 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:28,782 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   7%|▋         | 148/2124 [01:24<18:10,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5386
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5375
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5374
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5385
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5371
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5062
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:34,504 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:34,504 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:34,504 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   7%|▋         | 158/2124 [01:30<18:24,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5054
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5373
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5376
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5073
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5034
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5379
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5376
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:40,082 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:40,082 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:40,082 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   8%|▊         | 168/2124 [01:36<19:32,  1.67it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4760
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:45,930 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:45,930 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:45,931 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   8%|▊         | 178/2124 [01:41<18:02,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4452
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:51,470 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:51,471 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:51,471 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   9%|▉         | 188/2124 [01:47<17:51,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:44:57,015 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:44:57,015 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:44:57,015 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:   9%|▉         | 198/2124 [01:52<17:55,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:02,780 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:02,780 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:02,780 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  10%|▉         | 208/2124 [01:58<17:56,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4928
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:08,657 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:08,657 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:08,657 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  10%|█         | 218/2124 [02:04<17:51,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:14,211 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:14,211 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:14,211 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  11%|█         | 228/2124 [02:10<17:32,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:19,759 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:19,760 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:19,760 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  11%|█         | 238/2124 [02:15<17:23,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:25,283 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:25,283 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:25,283 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  12%|█▏        | 248/2124 [02:21<17:23,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:31,055 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:31,055 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:31,055 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  12%|█▏        | 258/2124 [02:26<17:36,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5073
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4762
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5377
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5378
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:36,689 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:36,689 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:36,689 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  13%|█▎        | 268/2124 [02:32<17:29,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5723
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4930
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5378
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5379
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5374
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5375
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:42,574 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:42,574 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:42,574 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  13%|█▎        | 278/2124 [02:38<17:03,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5388
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5063
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5375
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5385
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5386
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5066
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:48,117 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:48,117 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:48,117 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  14%|█▎        | 288/2124 [02:43<16:51,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5029
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5073
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5071
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5387
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5064
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5377
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5377
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5387
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:53,638 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:53,638 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:53,638 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  14%|█▍        | 298/2124 [02:49<16:49,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5071
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5380
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5379
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5386
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5376
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5038
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:45:59,365 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:45:59,365 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:45:59,365 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  15%|█▍        | 308/2124 [02:55<16:54,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4757
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4384
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4757
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:04,917 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:04,917 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:04,917 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  15%|█▍        | 318/2124 [03:01<20:13,  1.49it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5383
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:10,820 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:10,820 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:10,820 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  15%|█▌        | 328/2124 [03:06<16:35,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:16,326 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:16,326 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:16,326 - __main__ - INFO -   item_edge_type2: nnz=19

Epoch 1/1:  16%|█▌        | 338/2124 [03:12<16:32,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:21,859 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:21,860 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:21,860 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  16%|█▋        | 348/2124 [03:17<16:17,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:27,572 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:27,572 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:27,572 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  17%|█▋        | 358/2124 [03:23<16:23,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:33,117 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:33,117 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:33,117 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  17%|█▋        | 368/2124 [03:28<16:02,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4451
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:38,612 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:38,612 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:38,612 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  18%|█▊        | 378/2124 [03:34<19:46,  1.47it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:44,538 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:44,538 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:44,538 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  18%|█▊        | 388/2124 [03:40<16:01,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:50,060 - __main__ - INFO -   user_edge_type2: nnz=19
2025-05-29 09:46:50,060 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:50,060 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  19%|█▊        | 398/2124 [03:45<15:57,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4691
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:46:55,788 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:46:55,788 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:46:55,788 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  19%|█▉        | 408/2124 [03:51<16:08,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:01,408 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:01,408 - __main__ - INFO -   item_edge_type1: nnz=19
2025-05-29 09:47:01,408 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  20%|█▉        | 418/2124 [03:57<15:50,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:07,009 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:07,009 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:07,009 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  20%|██        | 428/2124 [04:02<15:40,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:12,574 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:12,574 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:12,574 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  21%|██        | 438/2124 [04:08<15:36,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:18,127 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:18,127 - __main__ - INFO -   item_edge_type1: nnz=19
2025-05-29 09:47:18,127 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  21%|██        | 448/2124 [04:14<16:05,  1.74it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:24,353 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:24,354 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:24,354 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  22%|██▏       | 458/2124 [04:20<15:38,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:29,958 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:29,958 - __main__ - INFO -   item_edge_type1: nnz=19
2025-05-29 09:47:29,958 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  22%|██▏       | 468/2124 [04:25<15:21,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:35,516 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:35,516 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:35,517 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  23%|██▎       | 478/2124 [04:31<15:14,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:41,081 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:41,081 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:41,081 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  23%|██▎       | 488/2124 [04:36<15:12,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:46,645 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:46,645 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:46,645 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  23%|██▎       | 498/2124 [04:42<15:05,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:52,409 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:52,410 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:52,410 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  24%|██▍       | 508/2124 [04:48<15:08,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4725
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:47:58,582 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:47:58,582 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:47:58,582 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  24%|██▍       | 518/2124 [04:54<15:08,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:04,193 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:04,194 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:04,194 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  25%|██▍       | 528/2124 [05:00<14:55,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:09,814 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:09,814 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:09,814 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  25%|██▌       | 538/2124 [05:05<14:57,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:15,437 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:15,437 - __main__ - INFO -   item_edge_type1: nnz=19
2025-05-29 09:48:15,437 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  26%|██▌       | 548/2124 [05:11<14:35,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4928
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:21,204 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:21,204 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:21,204 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  26%|██▋       | 558/2124 [05:17<14:35,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:26,785 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:26,785 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:26,785 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  27%|██▋       | 568/2124 [05:22<14:24,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:32,333 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:32,333 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:32,334 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  27%|██▋       | 578/2124 [05:28<14:13,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:38,496 - __main__ - INFO -   user_edge_type2: nnz=19
2025-05-29 09:48:38,496 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:38,496 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  28%|██▊       | 588/2124 [05:34<14:26,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:44,036 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:44,036 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:44,036 - __main__ - INFO -   item_edge_type2: nnz=19

Epoch 1/1:  28%|██▊       | 598/2124 [05:39<14:11,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:49,803 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:49,803 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:49,803 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  29%|██▊       | 608/2124 [05:45<14:18,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:48:55,409 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:48:55,409 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:48:55,409 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  29%|██▉       | 618/2124 [05:51<14:01,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:01,000 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:01,000 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:01,000 - __main__ - INFO -   item_edge_type2: nnz=19

Epoch 1/1:  30%|██▉       | 628/2124 [05:56<14:02,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:06,613 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:06,614 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:06,614 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  30%|███       | 638/2124 [06:02<13:45,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4725
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:12,197 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:12,197 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:12,197 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  31%|███       | 648/2124 [06:08<13:49,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:18,016 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:18,016 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:18,016 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  31%|███       | 658/2124 [06:14<14:30,  1.68it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:24,331 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:24,331 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:24,331 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  31%|███▏      | 668/2124 [06:20<13:36,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:29,937 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:29,937 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:29,937 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  32%|███▏      | 678/2124 [06:25<13:30,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:35,520 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:35,520 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:35,521 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  32%|███▏      | 688/2124 [06:31<13:33,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:41,141 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:41,141 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:41,141 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  33%|███▎      | 698/2124 [06:37<13:15,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4928
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:46,916 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:46,917 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:46,917 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  33%|███▎      | 708/2124 [06:42<13:16,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:52,500 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:52,501 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:52,501 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  34%|███▍      | 718/2124 [06:48<13:04,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5036
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:49:58,067 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:49:58,067 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:49:58,067 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  34%|███▍      | 728/2124 [06:53<12:58,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:04,362 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:04,362 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:04,363 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  35%|███▍      | 738/2124 [07:00<13:13,  1.75it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:09,942 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:09,942 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:09,942 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  35%|███▌      | 748/2124 [07:05<12:48,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:15,713 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:15,713 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:15,713 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  36%|███▌      | 758/2124 [07:11<12:49,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4760
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:21,337 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:21,337 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:21,337 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  36%|███▌      | 768/2124 [07:17<12:36,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:26,898 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:26,898 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:26,898 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  37%|███▋      | 778/2124 [07:22<12:30,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:32,471 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:32,471 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:32,471 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  37%|███▋      | 788/2124 [07:28<12:16,  1.81it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:38,005 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:38,005 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:38,006 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  38%|███▊      | 798/2124 [07:33<12:17,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:43,764 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:43,764 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:43,764 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  38%|███▊      | 808/2124 [07:39<12:21,  1.78it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:50,111 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:50,111 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:50,112 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  39%|███▊      | 818/2124 [07:45<12:18,  1.77it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4451
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:50:55,654 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:50:55,654 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:50:55,654 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  39%|███▉      | 828/2124 [07:51<12:02,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:01,218 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:01,218 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:01,218 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  39%|███▉      | 838/2124 [07:57<11:53,  1.80it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:06,784 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:06,784 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:06,784 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  40%|███▉      | 848/2124 [08:02<11:51,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:12,549 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:12,549 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:12,549 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  40%|████      | 858/2124 [08:08<11:47,  1.79it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4451
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:18,100 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:18,100 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:18,100 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  41%|████      | 868/2124 [08:13<11:28,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:23,598 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:23,598 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:23,598 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  41%|████▏     | 878/2124 [08:19<11:24,  1.82it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5035
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:29,104 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:29,104 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:29,104 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  42%|████▏     | 888/2124 [08:25<14:49,  1.39it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5070
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:35,407 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:35,407 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:35,407 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  42%|████▏     | 898/2124 [08:31<11:45,  1.74it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4759
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:41,322 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:41,323 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:41,323 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  43%|████▎     | 908/2124 [08:37<11:57,  1.70it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5382
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.4451
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
2025-05-29 09:51:47,215 - __main__ - INFO -   user_edge_type2: nnz=20
2025-05-29 09:51:47,215 - __main__ - INFO -   item_edge_type1: nnz=20
2025-05-29 09:51:47,215 - __main__ - INFO -   item_edge_type2: nnz=20

Epoch 1/1:  43%|████▎     | 916/2124 [08:42<11:48,  1.71it/s]
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5381
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
[Debug] Raw Grad Norm: 10.5069
[Debug] Grad Norm: 1.0000 | Update Norm: 0.0010
[Debug] Update Ratio: 0.001000
Lambda values: λ1=0.8000, λ2=0.7994, λ3=0.7995, λ4=1.5000, λ5=1.5000
