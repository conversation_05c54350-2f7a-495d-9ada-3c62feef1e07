2025-05-29 07:58:16,196 INFO    MainThread:173709 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-29 07:58:16,196 INFO    MainThread:173709 [wandb_setup.py:_flush():68] Configure stats pid to 173709
2025-05-29 07:58:16,196 INFO    MainThread:173709 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-29 07:58:16,196 INFO    MainThread:173709 [wandb_setup.py:_flush():68] Loading settings from /root/code/GraphLLM4CTR/models/wandb/settings
2025-05-29 07:58:16,196 INFO    MainThread:173709 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_075816-2km8pemj/logs/debug.log
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/GraphLLM4CTR/models/wandb/run-20250529_075816-2km8pemj/logs/debug-internal.log
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:init():852] calling init triggers
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:init():893] starting backend
2025-05-29 07:58:16,197 INFO    MainThread:173709 [wandb_init.py:init():897] sending inform_init request
2025-05-29 07:58:16,218 INFO    MainThread:173709 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 07:58:16,218 INFO    MainThread:173709 [wandb_init.py:init():907] backend started and connected
2025-05-29 07:58:16,221 INFO    MainThread:173709 [wandb_init.py:init():1002] updated telemetry
2025-05-29 07:58:16,276 INFO    MainThread:173709 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
