#!/usr/bin/env python3

import os
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_auc_score, accuracy_score, log_loss
import argparse

def analyze_ctr_results(results_dir):
    """
    Analyze CTR results to understand why accuracy is same but AUC differs
    """
    print("=== CTR Results Analysis ===")
    
    # Find all CTR result directories
    ctr_dirs = []
    if os.path.exists(results_dir):
        for item in os.listdir(results_dir):
            if item.startswith('ctr_temp_') and os.path.isdir(os.path.join(results_dir, item)):
                ctr_dirs.append(os.path.join(results_dir, item))
    
    if not ctr_dirs:
        print(f"❌ No CTR result directories found in {results_dir}")
        return
    
    ctr_dirs.sort()
    print(f"Found {len(ctr_dirs)} CTR result directories:")
    for ctr_dir in ctr_dirs:
        print(f"  - {os.path.basename(ctr_dir)}")
    
    # Look for summary CSV file
    summary_files = []
    for file in os.listdir(results_dir):
        if file.startswith('temperature_summary_') and file.endswith('.csv'):
            summary_files.append(os.path.join(results_dir, file))
    
    if summary_files:
        # Analyze summary file
        summary_file = sorted(summary_files)[-1]  # Use latest
        print(f"\n--- Analyzing Summary File: {os.path.basename(summary_file)} ---")
        
        df = pd.read_csv(summary_file)
        print("Summary Results:")
        print(df.to_string(index=False))
        
        # Check for identical accuracies
        if 'Accuracy' in df.columns:
            unique_accuracies = df['Accuracy'].nunique()
            if unique_accuracies == 1:
                print(f"\n❌ CONFIRMED: All accuracies are identical ({df['Accuracy'].iloc[0]})")
            else:
                print(f"\n✓ Accuracies vary ({unique_accuracies} unique values)")
        
        # Check AUC variation
        if 'AUC' in df.columns:
            auc_std = df['AUC'].std()
            auc_range = df['AUC'].max() - df['AUC'].min()
            print(f"AUC variation: std={auc_std:.4f}, range={auc_range:.4f}")
            
            if auc_range > 0.001:  # Meaningful difference
                print("✓ AUC values show meaningful variation")
            else:
                print("❌ AUC values are also very similar")
    
    # Analyze individual log files for probability distributions
    print(f"\n--- Analyzing Individual CTR Training Logs ---")
    
    temp_results = {}
    
    for ctr_dir in ctr_dirs:
        temp_value = os.path.basename(ctr_dir).replace('ctr_temp_', '')
        log_file = os.path.join(ctr_dir, 'ctr_training.log')
        
        if not os.path.exists(log_file):
            print(f"❌ Log file not found for temperature {temp_value}")
            continue
        
        # Extract test results from log
        with open(log_file, 'r') as f:
            content = f.read()
        
        # Look for test results line
        test_lines = [line for line in content.split('\n') if '[Test Results]' in line]
        
        if test_lines:
            test_line = test_lines[-1]  # Use last occurrence
            print(f"Temperature {temp_value}: {test_line}")
            
            # Parse metrics
            try:
                auc = float(test_line.split('AUC: ')[1].split(' |')[0])
                accuracy = float(test_line.split('Accuracy: ')[1].split(' |')[0])
                logloss = float(test_line.split('LogLoss: ')[1])
                
                temp_results[temp_value] = {
                    'auc': auc,
                    'accuracy': accuracy,
                    'logloss': logloss
                }
            except:
                print(f"❌ Failed to parse metrics for temperature {temp_value}")
    
    # Detailed analysis
    if len(temp_results) >= 2:
        print(f"\n=== DETAILED ANALYSIS ===")
        
        temps = sorted(temp_results.keys(), key=float)
        accuracies = [temp_results[t]['accuracy'] for t in temps]
        aucs = [temp_results[t]['auc'] for t in temps]
        loglosses = [temp_results[t]['logloss'] for t in temps]
        
        print(f"Temperature values: {temps}")
        print(f"Accuracies: {accuracies}")
        print(f"AUCs: {aucs}")
        print(f"LogLosses: {loglosses}")
        
        # Check patterns
        acc_identical = len(set(accuracies)) == 1
        auc_varies = max(aucs) - min(aucs) > 0.001
        
        print(f"\nPattern Analysis:")
        print(f"- Identical accuracies: {'✓' if acc_identical else '✗'}")
        print(f"- AUC variation: {'✓' if auc_varies else '✗'}")
        
        if acc_identical and auc_varies:
            print(f"\n🎯 DIAGNOSIS: Classic probability calibration issue!")
            print(f"   - Same decision boundary (accuracy = {accuracies[0]:.4f})")
            print(f"   - Different probability distributions (AUC range: {min(aucs):.4f}-{max(aucs):.4f})")
            print(f"   - This suggests temperature affects embedding quality/confidence")
            
            # Analyze LogLoss for additional insights
            if max(loglosses) - min(loglosses) > 0.01:
                print(f"   - LogLoss also varies ({min(loglosses):.4f}-{max(loglosses):.4f})")
                print(f"   - Lower LogLoss = better calibrated probabilities")
                
                best_temp_idx = loglosses.index(min(loglosses))
                best_temp = temps[best_temp_idx]
                print(f"   - Best calibrated temperature: {best_temp} (LogLoss: {min(loglosses):.4f})")
        
        # Create visualization
        create_metrics_plot(temps, accuracies, aucs, loglosses, results_dir)

def create_metrics_plot(temps, accuracies, aucs, loglosses, save_dir):
    """Create visualization of metrics across temperatures"""
    
    fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(15, 5))
    
    temps_float = [float(t) for t in temps]
    
    # Accuracy plot
    ax1.plot(temps_float, accuracies, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Temperature')
    ax1.set_ylabel('Accuracy')
    ax1.set_title('Accuracy vs Temperature')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim([min(accuracies) - 0.001, max(accuracies) + 0.001])
    
    # AUC plot
    ax2.plot(temps_float, aucs, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('Temperature')
    ax2.set_ylabel('AUC')
    ax2.set_title('AUC vs Temperature')
    ax2.grid(True, alpha=0.3)
    
    # LogLoss plot
    ax3.plot(temps_float, loglosses, 'go-', linewidth=2, markersize=8)
    ax3.set_xlabel('Temperature')
    ax3.set_ylabel('LogLoss')
    ax3.set_title('LogLoss vs Temperature')
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    plot_path = os.path.join(save_dir, 'temperature_metrics_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"\n📊 Metrics plot saved to: {plot_path}")
    plt.close()

def suggest_solutions():
    """Suggest solutions based on the analysis"""
    print(f"\n=== RECOMMENDED SOLUTIONS ===")
    print(f"1. 🎯 This is actually GOOD news - your temperature experiments are working!")
    print(f"   - Different temperatures create different embedding qualities")
    print(f"   - Same accuracy means consistent decision boundary")
    print(f"   - Different AUC means better probability calibration")
    
    print(f"\n2. 📊 Choose temperature based on:")
    print(f"   - Highest AUC (best ranking performance)")
    print(f"   - Lowest LogLoss (best probability calibration)")
    print(f"   - Consider validation set performance")
    
    print(f"\n3. 🔍 For deeper analysis, you could:")
    print(f"   - Plot probability histograms for each temperature")
    print(f"   - Analyze confidence intervals around 0.5 threshold")
    print(f"   - Check precision/recall at different thresholds")
    
    print(f"\n4. ⚠️  If you want different accuracies:")
    print(f"   - Use different random seeds")
    print(f"   - Vary other hyperparameters (learning rate, epochs)")
    print(f"   - Check if embeddings are actually different")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", type=str, required=True,
                       help="Directory containing CTR results")
    args = parser.parse_args()
    
    analyze_ctr_results(args.results_dir)
    suggest_solutions()
