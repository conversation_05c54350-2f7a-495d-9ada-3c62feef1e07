#!/usr/bin/env python3

import os
import torch
import pandas as pd
import numpy as np
from pathlib import Path
import argparse

def analyze_temperature_embeddings(base_dir):
    """
    Analyze embeddings from different temperature experiments to identify why 
    CTR results are identical across temperatures.
    """
    print("=== Temperature Embedding Analysis ===")
    print(f"Base directory: {base_dir}")
    
    # Find all temperature directories
    temp_dirs = []
    if os.path.exists(base_dir):
        for item in os.listdir(base_dir):
            item_path = os.path.join(base_dir, item)
            if os.path.isdir(item_path) and item.startswith('temp_'):
                temp_dirs.append(item_path)
    
    if not temp_dirs:
        print(f"❌ No temperature directories found in {base_dir}")
        return False
    
    temp_dirs.sort()
    print(f"Found {len(temp_dirs)} temperature directories:")
    for temp_dir in temp_dirs:
        print(f"  - {os.path.basename(temp_dir)}")
    
    # Analyze each temperature directory
    embedding_stats = {}
    
    for temp_dir in temp_dirs:
        temp_value = os.path.basename(temp_dir).replace('temp_', '')
        print(f"\n--- Analyzing Temperature {temp_value} ---")
        
        # Check for embedding directories
        embedding_dirs = []
        for subdir in ['aligned_embeddings_epoch1', 'aligned_embeddings_final']:
            emb_dir = os.path.join(temp_dir, subdir)
            if os.path.exists(emb_dir):
                embedding_dirs.append(emb_dir)
        
        if not embedding_dirs:
            print(f"❌ No embedding directories found in {temp_dir}")
            continue
        
        # Analyze the first available embedding directory
        emb_dir = embedding_dirs[0]
        print(f"Using embedding directory: {os.path.basename(emb_dir)}")
        
        # Check for required embedding files
        required_files = [
            'user_graph_proj.pt', 'user_graph_proj_id_mapping.csv',
            'item_graph_proj.pt', 'item_graph_proj_id_mapping.csv',
            'user_click_proj.pt', 'user_click_proj_id_mapping.csv',
            'user_impression_proj.pt', 'user_impression_proj_id_mapping.csv',
            'item_clicked_proj.pt', 'item_clicked_proj_id_mapping.csv',
            'item_impressed_proj.pt', 'item_impressed_proj_id_mapping.csv'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_name in required_files:
            file_path = os.path.join(emb_dir, file_name)
            if os.path.exists(file_path):
                existing_files.append(file_name)
            else:
                missing_files.append(file_name)
        
        print(f"✓ Found {len(existing_files)} embedding files")
        if missing_files:
            print(f"❌ Missing {len(missing_files)} files: {missing_files[:3]}...")
        
        # Analyze embedding statistics
        stats = {}
        for file_name in existing_files:
            if file_name.endswith('.pt'):
                file_path = os.path.join(emb_dir, file_name)
                try:
                    embedding = torch.load(file_path, map_location='cpu')
                    stats[file_name] = {
                        'shape': list(embedding.shape),
                        'mean': float(embedding.mean()),
                        'std': float(embedding.std()),
                        'min': float(embedding.min()),
                        'max': float(embedding.max()),
                        'norm': float(embedding.norm())
                    }
                except Exception as e:
                    print(f"❌ Error loading {file_name}: {e}")
        
        embedding_stats[temp_value] = stats
        
        # Print summary for this temperature
        if stats:
            print(f"Embedding statistics summary:")
            for emb_name, emb_stats in list(stats.items())[:3]:  # Show first 3
                print(f"  {emb_name}: shape={emb_stats['shape']}, mean={emb_stats['mean']:.4f}, std={emb_stats['std']:.4f}")
    
    # Compare embeddings across temperatures
    print(f"\n=== Cross-Temperature Comparison ===")
    
    if len(embedding_stats) < 2:
        print("❌ Need at least 2 temperature results for comparison")
        return False
    
    # Compare the same embedding type across temperatures
    temp_values = list(embedding_stats.keys())
    common_embeddings = set(embedding_stats[temp_values[0]].keys())
    
    for temp_val in temp_values[1:]:
        common_embeddings &= set(embedding_stats[temp_val].keys())
    
    if not common_embeddings:
        print("❌ No common embeddings found across temperatures")
        return False
    
    print(f"Comparing {len(common_embeddings)} common embedding types across {len(temp_values)} temperatures")
    
    identical_embeddings = []
    different_embeddings = []
    
    for emb_name in common_embeddings:
        # Compare statistics across temperatures
        means = [embedding_stats[temp][emb_name]['mean'] for temp in temp_values]
        stds = [embedding_stats[temp][emb_name]['std'] for temp in temp_values]
        norms = [embedding_stats[temp][emb_name]['norm'] for temp in temp_values]
        
        # Check if all statistics are very similar (indicating identical embeddings)
        mean_diff = max(means) - min(means)
        std_diff = max(stds) - min(stds)
        norm_diff = max(norms) - min(norms)
        
        if mean_diff < 1e-6 and std_diff < 1e-6 and norm_diff < 1e-6:
            identical_embeddings.append(emb_name)
        else:
            different_embeddings.append(emb_name)
        
        print(f"{emb_name}:")
        print(f"  Mean range: {min(means):.6f} - {max(means):.6f} (diff: {mean_diff:.6f})")
        print(f"  Std range:  {min(stds):.6f} - {max(stds):.6f} (diff: {std_diff:.6f})")
        print(f"  Norm range: {min(norms):.6f} - {max(norms):.6f} (diff: {norm_diff:.6f})")
        print(f"  Status: {'❌ IDENTICAL' if emb_name in identical_embeddings else '✓ Different'}")
        print()
    
    # Final diagnosis
    print(f"=== DIAGNOSIS ===")
    if identical_embeddings:
        print(f"❌ PROBLEM FOUND: {len(identical_embeddings)} embedding types are identical across temperatures!")
        print(f"Identical embeddings: {identical_embeddings}")
        print(f"This explains why CTR accuracy is the same (0.6948) across temperatures.")
        print(f"\nPossible causes:")
        print(f"1. Contrastive learning didn't run properly for different temperatures")
        print(f"2. Same model checkpoint is being used for all temperatures")
        print(f"3. Temperature parameter is not being applied correctly")
        print(f"4. Embeddings are being overwritten or not saved properly")
    else:
        print(f"✓ Embeddings are different across temperatures ({len(different_embeddings)} types)")
        print(f"The problem might be elsewhere (e.g., CTR model initialization, data loading)")
    
    return len(identical_embeddings) == 0

def check_qformer_checkpoints(base_dir):
    """Check if QFormer checkpoints are different across temperatures"""
    print(f"\n=== QFormer Checkpoint Analysis ===")
    
    temp_dirs = [d for d in os.listdir(base_dir) if d.startswith('temp_') and os.path.isdir(os.path.join(base_dir, d))]
    temp_dirs.sort()
    
    checkpoint_stats = {}
    
    for temp_dir in temp_dirs:
        temp_value = temp_dir.replace('temp_', '')
        temp_path = os.path.join(base_dir, temp_dir)
        
        # Look for QFormer checkpoints
        qformer_files = []
        for file_name in os.listdir(temp_path):
            if file_name.startswith('qformer_') and file_name.endswith('.pt'):
                qformer_files.append(file_name)
        
        if not qformer_files:
            print(f"❌ No QFormer checkpoint found for temperature {temp_value}")
            continue
        
        # Use the first checkpoint found
        qformer_file = qformer_files[0]
        qformer_path = os.path.join(temp_path, qformer_file)
        
        try:
            checkpoint = torch.load(qformer_path, map_location='cpu')
            
            # Extract model state dict
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # Calculate statistics for the model parameters
            all_params = []
            for param_name, param_tensor in state_dict.items():
                all_params.append(param_tensor.flatten())
            
            if all_params:
                all_params_tensor = torch.cat(all_params)
                checkpoint_stats[temp_value] = {
                    'mean': float(all_params_tensor.mean()),
                    'std': float(all_params_tensor.std()),
                    'norm': float(all_params_tensor.norm()),
                    'num_params': len(all_params_tensor)
                }
                print(f"Temperature {temp_value}: {len(state_dict)} parameters, norm={checkpoint_stats[temp_value]['norm']:.6f}")
        
        except Exception as e:
            print(f"❌ Error loading QFormer checkpoint for temperature {temp_value}: {e}")
    
    # Compare QFormer checkpoints
    if len(checkpoint_stats) >= 2:
        temp_values = list(checkpoint_stats.keys())
        norms = [checkpoint_stats[temp]['norm'] for temp in temp_values]
        means = [checkpoint_stats[temp]['mean'] for temp in temp_values]
        
        norm_diff = max(norms) - min(norms)
        mean_diff = max(means) - min(means)
        
        print(f"\nQFormer checkpoint comparison:")
        print(f"Norm range: {min(norms):.6f} - {max(norms):.6f} (diff: {norm_diff:.6f})")
        print(f"Mean range: {min(means):.6f} - {max(means):.6f} (diff: {mean_diff:.6f})")
        
        if norm_diff < 1e-6 and mean_diff < 1e-6:
            print("❌ QFormer checkpoints are identical across temperatures!")
        else:
            print("✓ QFormer checkpoints are different across temperatures")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--base_dir", type=str, required=True, 
                       help="Base directory containing temperature analysis results")
    args = parser.parse_args()
    
    success = analyze_temperature_embeddings(args.base_dir)
    check_qformer_checkpoints(args.base_dir)
    
    if not success:
        print(f"\n❌ Analysis failed. Please check if the temperature experiments ran correctly.")
    else:
        print(f"\n✓ Analysis completed successfully.")
