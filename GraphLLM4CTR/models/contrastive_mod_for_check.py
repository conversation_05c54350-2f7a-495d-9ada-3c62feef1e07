import torch
import torch.nn as nn
import torch.nn.functional as F
from qformer import GraphQFormer, TextQFormer
from tqdm import tqdm

class MultiModalContrastiveLoss(nn.Module):
    def __init__(self, temperature=0.07, structure_weight=0.7, strong_neg_weight=1.0, weak_neg_weight=0.3):
        """
        Args:
            temperature: Temperature parameter for similarity scaling
            structure_weight: Weight for subgraph in structure-based losses
            strong_neg_weight: Weight for strong negative samples in contrastive loss
            weak_neg_weight: Weight for weak negative samples in contrastive loss
        """
        super().__init__()
        # ✅ FIX: Make temperature non-learnable to preserve input temperature
        self.register_buffer('temp', torch.tensor(temperature))
        self.structure_weight = structure_weight
        self.strong_neg_weight = strong_neg_weight
        self.weak_neg_weight = weak_neg_weight

    def user_item_contrastive_loss(self, user_embeds, item_embeds, click_matrix, impression_matrix):
        if len(user_embeds.shape) == 3:
            user_embeds = user_embeds.mean(1)
        if len(item_embeds.shape) == 3:
            item_embeds = item_embeds.mean(1)

        user_embeds = F.normalize(user_embeds, dim=-1)
        item_embeds = F.normalize(item_embeds, dim=-1)

        sim_matrix = torch.matmul(user_embeds, item_embeds.T) / self.temp
        sim_matrix = torch.clamp(sim_matrix, min=-20, max=20)  # 防止 logsumexp 溢出

        if isinstance(click_matrix, torch.sparse.Tensor):
            click_matrix = click_matrix.to_dense()
        if isinstance(impression_matrix, torch.sparse.Tensor):
            impression_matrix = impression_matrix.to_dense()

        positives = click_matrix.bool()
        high_negs = impression_matrix.bool() & (~positives)
        low_negs = ~(click_matrix.bool() | impression_matrix.bool())

        def directional_loss(a_embeds, b_embeds, pos_mask, high_neg_mask, low_neg_mask):
            sim = torch.matmul(a_embeds, b_embeds.T) / self.temp
            sim = torch.clamp(sim, min=-20, max=20)  # again for safety

            loss = 0.0
            count = 0

            for i in range(sim.size(0)):
                pos_idx = pos_mask[i].nonzero(as_tuple=True)[0]
                if pos_idx.numel() == 0:
                    continue

                pos_sims = sim[i][pos_idx]
                pos_term = (1.5 * pos_sims).mean()

                high_idx = high_neg_mask[i].nonzero(as_tuple=True)[0]
                low_idx = low_neg_mask[i].nonzero(as_tuple=True)[0]

                neg_sims = []
                if high_idx.numel() > 0:
                    neg_sims.append(self.strong_neg_weight * sim[i][high_idx])
                if low_idx.numel() > 0:
                    neg_sims.append(self.weak_neg_weight * sim[i][low_idx])

                if neg_sims:
                    all_negs = torch.cat(neg_sims)
                    denom = torch.logsumexp(all_negs, dim=0)
                    loss_i = - (pos_term - denom)
                else:
                    loss_i = torch.tensor(0.0, device=sim.device)

                if torch.isnan(loss_i) or torch.isinf(loss_i):
                    continue  # 跳过异常样本

                loss += loss_i
                count += 1

            return loss / (count + 1e-6)  # 避免除以 0

        loss_u2v = directional_loss(user_embeds, item_embeds, positives, high_negs, low_negs)
        loss_v2u = directional_loss(item_embeds, user_embeds, positives.T, high_negs.T, low_negs.T)

        return 0.5 * (loss_u2v + loss_v2u)



    def structure_based_loss(self, node_embeds_type1, node_embeds_type2, edge_matrix_type1, edge_matrix_type2):
        """Structure-based Contrastive Loss for User/Item Graph with numerical stability"""

        def flatten_embedding(embed):
            if embed.ndim == 3:
                return embed.squeeze(1) if embed.size(1) == 1 else embed.mean(1)
            return embed

        node_embeds_type1 = F.normalize(flatten_embedding(node_embeds_type1), dim=-1)
        node_embeds_type2 = F.normalize(flatten_embedding(node_embeds_type2), dim=-1)

        def compute_subgraph_loss(embeds: torch.Tensor, edge_matrix: torch.sparse.Tensor, hard_neg_k: int = 32):
            embeds = F.normalize(embeds, dim=1)
            sim_matrix = torch.matmul(embeds, embeds.T) / self.temp

            edge_indices = edge_matrix._indices()
            batch_size = embeds.size(0)
            losses = []

            for i in range(batch_size):
                pos_idx = edge_indices[1][edge_indices[0] == i]
                if pos_idx.numel() == 0:
                    continue

                pos_logits = sim_matrix[i, pos_idx]
                neg_mask = torch.ones_like(sim_matrix[i], dtype=torch.bool)
                neg_mask[i] = False
                neg_mask[pos_idx] = False
                neg_logits = sim_matrix[i][neg_mask]

                if neg_logits.numel() > hard_neg_k:
                    hard_neg = torch.topk(neg_logits, hard_neg_k).values
                else:
                    hard_neg = neg_logits

                # 计算 logsumexp 部分时加 clamp 限制
                all_logits = torch.cat([pos_logits, hard_neg]) if hard_neg.numel() > 0 else pos_logits
                all_logits = torch.clamp(all_logits, max=50)

                weighted_pos = torch.clamp(pos_logits.mean() * 1.5, min=1e-6, max=50)
                loss = - (weighted_pos - torch.logsumexp(all_logits, dim=0))
                loss = torch.clamp(loss, min=0.0, max=50.0)  # 限制异常 loss

                losses.append(loss)

            return torch.stack(losses).mean() if losses else torch.tensor(0.0, requires_grad=True).to(embeds.device)

        loss_type1 = compute_subgraph_loss(node_embeds_type1, edge_matrix_type1)
        loss_type2 = compute_subgraph_loss(node_embeds_type2, edge_matrix_type2)

        return self.structure_weight * loss_type1 + (1 - self.structure_weight) * loss_type2


    def modality_alignment_loss(self, graph_embeds, text_embeds):
        """Cross-modal Alignment Loss"""
        # Handle 3D tensors
        if len(graph_embeds.shape) == 3:
            graph_embeds = graph_embeds.mean(1)  # [batch_size, hidden_dim]
        if len(text_embeds.shape) == 3:
            text_embeds = text_embeds.mean(1)  # [batch_size, hidden_dim]

        # Check for dimension mismatch
        if graph_embeds.shape[-1] != text_embeds.shape[-1]:
            print(f"WARNING: Dimension mismatch - graph: {graph_embeds.shape}, text: {text_embeds.shape}")
            return torch.tensor(0.0, device=graph_embeds.device, requires_grad=True)

        # Normalize embeddings
        graph_embeds = F.normalize(graph_embeds, dim=-1)
        text_embeds = F.normalize(text_embeds, dim=-1)

        # Check for NaN after normalization
        if torch.isnan(graph_embeds).any() or torch.isnan(text_embeds).any():
            print(f"WARNING: NaN detected after normalization")
            return torch.tensor(0.0, device=graph_embeds.device, requires_grad=True)

        # Compute similarity matrix
        sim_matrix = torch.matmul(graph_embeds, text_embeds.t()) / self.temp

        # Clamp similarity matrix to prevent overflow
        sim_matrix = torch.clamp(sim_matrix, min=-20, max=20)

        # Labels for positive pairs (diagonal elements)
        labels = torch.arange(len(graph_embeds), device=graph_embeds.device)

        # Compute bidirectional loss
        loss_g2t = F.cross_entropy(sim_matrix, labels)
        loss_t2g = F.cross_entropy(sim_matrix.t(), labels)

        return (loss_g2t + loss_t2g) / 2

class ContrastiveLearner(nn.Module):
    def __init__(self, graph_hidden_dim=96, text_hidden_dim=768, common_dim=256, num_queries=32, temperature=0.07, structure_weight=0.7, strong_neg_weight=1.0, weak_neg_weight=0.3, num_heads=8):
        super().__init__()

        # Q-Formers
        self.graph_qformer = GraphQFormer(
            graph_embedding_dim=144,  # Input dimension
            hidden_dim=graph_hidden_dim,  # Output dimension (96)
            num_queries=num_queries,
            num_heads=num_heads
        )

        self.text_qformer = TextQFormer(
            text_embedding_dim=768,  # Input dimension for text
            hidden_dim=text_hidden_dim,  # Output dimension (768)
            num_queries=num_queries,
            num_heads=num_heads
        )

        self.graph_proj = nn.Sequential(
            nn.Linear(graph_hidden_dim, common_dim),
            nn.LayerNorm(common_dim),
            nn.ReLU(),
            nn.Linear(common_dim, common_dim)
        )

        self.text_proj = nn.Sequential(
            nn.Linear(text_hidden_dim, common_dim),
            nn.LayerNorm(common_dim),
            nn.ReLU(),
            nn.Linear(common_dim, common_dim)
        )

        # Initialize learnable lambda parameters
        self.lambda1 = nn.Parameter(torch.ones([]) * 1.0)
        self.lambda2 = nn.Parameter(torch.ones([]) * 1.0)
        self.lambda3 = nn.Parameter(torch.ones([]) * 1.0)
        self.lambda4 = nn.Parameter(torch.ones([]) * 1.0)
        self.lambda5 = nn.Parameter(torch.ones([]) * 1.0)

        # # DEBUG
        # self.lambda1 = 1.0
        # self.lambda2 = 0.8
        # self.lambda3 = 0.8
        # self.lambda4 = 2.0
        # self.lambda5 = 2.0



        # Base contrastive loss module
        self.contrastive_loss = MultiModalContrastiveLoss(
            temperature=temperature,
            structure_weight=structure_weight,
            strong_neg_weight=strong_neg_weight,
            weak_neg_weight=weak_neg_weight
        )

    def prune_dense_edge_matrix(self, edge_matrix, k):
        edge_matrix = edge_matrix.clone()
        values, indices = torch.topk(edge_matrix, k, dim=-1)
        mask = torch.zeros_like(edge_matrix).bool()
        mask.scatter_(1, indices, True)
        edge_matrix[~mask] = 0.0
        return edge_matrix

    def prune_sparse_topk(self, edge_matrix, k):
        indices = edge_matrix._indices()  # [2, E]
        values = edge_matrix._values()    # [E]
        num_nodes = edge_matrix.size(0)

        # 按行组织：src -> list of (dst, weight)
        row_to_edges = [[] for _ in range(num_nodes)]
        for idx in range(values.size(0)):
            src = indices[0, idx].item()
            dst = indices[1, idx].item()
            val = values[idx].item()
            row_to_edges[src].append((dst, val))

        new_src = []
        new_dst = []
        new_val = []

        for src, edges in enumerate(row_to_edges):
            if not edges:
                continue
            topk = sorted(edges, key=lambda x: -x[1])[:k]
            for dst, val in topk:
                new_src.append(src)
                new_dst.append(dst)
                new_val.append(val)

        new_indices = torch.tensor([new_src, new_dst], dtype=torch.long)
        new_values = torch.tensor(new_val, dtype=torch.float)
        return torch.sparse_coo_tensor(
            new_indices, new_values, size=edge_matrix.shape
        )


    def forward(self, batch):
        """
        Forward pass computing all contrastive losses

        Args:
            batch: Dictionary containing:
                user_item_user_embeddings
                user_item_item_embeddings
                user_co_click_embeddings
                user_co_impression_embeddings
                item_co_clicked_embeddings
                item_co_impressed_embeddings
                user_text_embeddings
                item_text_embeddings
                click_matrix: Click interaction matrix
                impression_matrix: Impression interaction matrix
                user_edge_type1: User strong connection matrix
                user_edge_type2: User weak connection matrix
                item_edge_type1: Item strong connection matrix
                item_edge_type2: Item weak connection matrix
        """

        # Process user-item graph embeddings
        user_graph_out = self.graph_qformer(batch['user_item_user_embeddings'])  # [batch, seq, 96]
        item_graph_out = self.graph_qformer(batch['user_item_item_embeddings'])  # [batch, seq, 96]

        # Process user-user and item-item graph embeddings
        user_click_out = self.graph_qformer(batch['user_co_click_embeddings'])
        user_impression_out = self.graph_qformer(batch['user_co_impression_embeddings'])
        item_clicked_out = self.graph_qformer(batch['item_co_clicked_embeddings'])
        item_impressed_out = self.graph_qformer(batch['item_co_impressed_embeddings'])

        # Process text embeddings
        user_text_out = self.text_qformer(batch['user_text_embeddings'])
        item_text_out = self.text_qformer(batch['item_text_embeddings'])

        # Project to common space - apply projection to 3D tensors directly
        user_graph_out_proj = self.graph_proj(user_graph_out)  # [batch, seq, 256]
        item_graph_out_proj = self.graph_proj(item_graph_out)  # [batch, seq, 256]
        user_click_out_proj = self.graph_proj(user_click_out)
        user_impression_out_proj = self.graph_proj(user_impression_out)
        item_clicked_out_proj = self.graph_proj(item_clicked_out)
        item_impressed_out_proj = self.graph_proj(item_impressed_out)
        user_text_out_proj = self.text_proj(user_text_out)
        item_text_out_proj = self.text_proj(item_text_out)


        # Loss1: User-Item Graph
        # print("DEBUG: click_matrix positive count =", batch["click_matrix"].sum().item())

        loss1 = self.contrastive_loss.user_item_contrastive_loss(
            user_graph_out_proj,
            item_graph_out_proj,
            batch['click_matrix'],
            batch['impression_matrix']
        )
        # print("DEBUG: user_graph_out_proj std =", user_graph_out_proj.std().item())
        # print("DEBUG: item_graph_out_proj std =", item_graph_out_proj.std().item())

        #DEBUG
        user_edge1 = self.prune_sparse_topk(batch['user_edge_type1'], k=30)
        user_edge2 = self.prune_sparse_topk(batch['user_edge_type2'], k=30)
        item_edge1 = self.prune_sparse_topk(batch['item_edge_type1'], k=50)
        item_edge2 = self.prune_sparse_topk(batch['item_edge_type2'], k=50)

        # Loss2: User Graph Structure
        loss2 = self.contrastive_loss.structure_based_loss(
            user_click_out_proj,
            user_impression_out_proj,
            user_edge1,
            user_edge2
        )

        if loss2.item() < 0.0:
            print("user_structure_loss < 0, frozen.")
            loss2 = loss2.detach()

        # Loss3: Item Graph Structure
        loss3 = self.contrastive_loss.structure_based_loss(
            item_clicked_out_proj,
            item_impressed_out_proj,
            item_edge1,
            item_edge2
        )

        if loss3.item() < 0.0:
            print("item_structure_loss < 0, frozen.")
            loss3 = loss3.detach()

        # Loss4: User Graph-Text Alignment
        user_average = torch.mean(torch.stack([user_click_out_proj, user_impression_out_proj]), dim=0)
        loss4 = self.contrastive_loss.modality_alignment_loss(
            user_average,
            user_text_out_proj
        )

        # Loss5: Item Graph-Text Alignment
        item_average = torch.mean(torch.stack([item_clicked_out_proj, item_impressed_out_proj]), dim=0)
        loss5 = self.contrastive_loss.modality_alignment_loss(
            item_average,
            item_text_out_proj
        )

        # Debug text alignment
        if torch.isnan(loss4) or torch.isnan(loss5):
            print(f"DEBUG: Text loss NaN detected!")
            print(f"  user_average shape: {user_average.shape}, std: {user_average.std().item():.6f}")
            print(f"  user_text_out_proj shape: {user_text_out_proj.shape}, std: {user_text_out_proj.std().item():.6f}")
            print(f"  item_average shape: {item_average.shape}, std: {item_average.std().item():.6f}")
            print(f"  item_text_out_proj shape: {item_text_out_proj.shape}, std: {item_text_out_proj.std().item():.6f}")

        # Combine all losses
        total_loss = (
            self.lambda1 * loss1 +
            self.lambda2 * loss2 +
            self.lambda3 * loss3 +
            self.lambda4 * loss4 +
            self.lambda5 * loss5
        )

        # DEBUG
        print(f"Lambda values: λ1={self.lambda1.item():.4f}, λ2={self.lambda2.item():.4f}, λ3={self.lambda3.item():.4f}, λ4={self.lambda4.item():.4f}, λ5={self.lambda5.item():.4f}")

        return {
            'total_loss': total_loss,
            'user_item_loss': loss1,
            'user_structure_loss': loss2,
            'item_structure_loss': loss3,
            'user_text_loss': loss4,
            'item_text_loss': loss5,
            'user_graph_proj': user_graph_out_proj,
            'item_graph_proj': item_graph_out_proj,
            'user_click_graph_proj': user_click_out_proj,
            'user_imp_graph_proj': user_impression_out_proj,
            'item_click_graph_proj': item_clicked_out_proj,
            'item_imp_graph_proj': item_impressed_out_proj,
            'user_text_proj': user_text_out_proj,
            'item_text_proj' : item_text_out_proj,
        }
