from sklearn.decomposition import TruncatedSVD
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.nn.functional as F

def safe_show(title="figure"):
    fname = title.lower().replace(" ", "_") + ".png"
    plt.savefig(fname)
    print(f"[Saved] {fname}")
    plt.close()

def visualize_svd(embedding_matrix, title="SVD Projection"):
    if isinstance(embedding_matrix, torch.Tensor) and embedding_matrix.dim() == 3:
        embedding_matrix = embedding_matrix.mean(dim=1)  # [N, 32, D] → [N, D]
    try:
        emb = F.normalize(embedding_matrix, dim=-1).cpu().numpy()
    except:
        emb = F.normalize(embedding_matrix, dim=-1).cpu().detach().numpy()
    svd = TruncatedSVD(n_components=2)
    proj = svd.fit_transform(emb)

    plt.figure(figsize=(6, 5))
    plt.scatter(proj[:, 0], proj[:, 1], s=5, alpha=0.6)
    plt.title(title)
    plt.xlabel("SVD-1")
    plt.ylabel("SVD-2")
    plt.grid(True)
    plt.tight_layout()
    safe_show(title)
    print('done')

if __name__ == "__main__":
    item_text = torch.load("/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.7/aligned_embeddings_epoch1/item_text_proj.pt")
    # item_click = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_clicked_proj.pt")
    # item_imp = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_impressed_proj.pt")
    # item_ = torch.load("/root/code/GraphLLM4CTR/models/contrastive_model_tmp/aligned_embeddings_epoch1/item_graph_proj.pt")
    # 求平均，与 user_text 进行可视化对比
    # item_graph = (item_click + item_imp) / 2
    # print('item_graph', item_graph.shape)
    print('item_text', item_text.shape)


    # before
    item_text_before = torch.load("/data/datasets/processed_datasets/amazon/text_embeddings/item_text_embeddings.pt")
    # item_click_before = torch.load("/data/datasets/processed_datasets/movielens/graph_embeddings/item_co_clicked_item_graph_embeddings.pt")
    # item_imp_before = torch.load("/data/datasets/processed_datasets/movielens/graph_embeddings/item_co_impressed_item_graph_embeddings.pt")
    # item_graph_before = (item_click_before + item_imp_before) / 2
    # visualize_tsne(item_graph_before, item_text_before)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    reduce_text = nn.Linear(768, 64).cpu()  # 放 CPU
    item_text_before = item_text_before.cpu()
    item_text_before_reduced = reduce_text(item_text_before).detach()

    # visualize_svd(item_graph, title="Item Graph Embedding (SVD)")
    visualize_svd(item_text, title="amazon Item Text Embedding (SVD)")
    # visualize_svd(item_graph_before, title="Item Graph Embedding (SVD) before")
    visualize_svd(item_text_before_reduced, title="amazon Item Text Embedding (SVD) before")