import torch
import matplotlib.pyplot as plt
import torch.nn.functional as F
import torch.nn as nn

def safe_show(title="figure"):
    fname = title.lower().replace(" ", "_") + ".png"
    plt.savefig(fname)
    print(f"[Saved] {fname}")
    plt.close()

def plot_cosine_similarity_distribution(graph_embed, text_embed, title="Graph-Text Cosine Similarity"):
    # 降维（如有必要）
    if graph_embed.dim() == 3:
        graph_embed = graph_embed.mean(dim=1)
    if text_embed.dim() == 3:
        text_embed = text_embed.mean(dim=1)

    # 归一化后计算配对相似度
    graph_embed = F.normalize(graph_embed, dim=-1)
    text_embed = F.normalize(text_embed, dim=-1)
    try:
        cos_sim = torch.sum(graph_embed * text_embed, dim=-1).cpu().numpy()
    except:
        graph_embed = graph_embed.cpu()
        text_embed = text_embed.cpu()
        cos_sim = torch.sum(graph_embed * text_embed, dim=-1).detach().numpy()

    # 可视化直方图
    plt.figure(figsize=(7, 5))
    plt.hist(cos_sim, bins=50, color='purple', alpha=0.7)
    plt.title(title)
    plt.xlabel("Cosine Similarity")
    plt.ylabel("Frequency")
    plt.grid(True)
    plt.tight_layout()
    plt.show()
    safe_show(title)
    print(f"平均余弦相似度: {cos_sim.mean():.4f}, 最大值: {cos_sim.max():.4f}, 最小值: {cos_sim.min():.4f}")



if __name__ == "__main__":

    item_text = torch.load("/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.7/aligned_embeddings_epoch1/item_text_proj.pt")
    item_click = torch.load("/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.7/aligned_embeddings_epoch1/item_clicked_proj.pt")
    item_imp = torch.load("/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.7/aligned_embeddings_epoch1/item_impressed_proj.pt")
    item_ = torch.load("/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/temp_0.7/aligned_embeddings_epoch1/item_graph_proj.pt")
    # 求平均，与 user_text 进行可视化对比
    item_graph = (item_click + item_imp) / 2
    print('item_graph', item_graph.shape)
    print('item_text', item_text.shape)

    # before
    item_text_before = torch.load("/data/datasets/processed_datasets/amazon/text_embeddings/item_text_embeddings.pt")
    item_click_before = torch.load("/data/datasets/processed_datasets/amazon/graph_embeddings/item_co_clicked_item_graph_embeddings.pt")
    item_imp_before = torch.load("/data/datasets/processed_datasets/amazon/graph_embeddings/item_co_impressed_item_graph_embeddings.pt")
    item_graph_before = (item_click_before + item_imp_before) / 2
    # visualize_tsne(item_graph_before, item_text_before)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    reduce_text = nn.Linear(768, 64).cpu()  # 放 CPU
    item_text_before = item_text_before.cpu()
    item_text_before_reduced = reduce_text(item_text_before).detach()

    # plot_cosine_similarity_distribution(item_graph_before, item_text_before_reduced, title="Before Contrastive Learning")
    plot_cosine_similarity_distribution(item_graph, item_text, title="After Contrastive Learning")
