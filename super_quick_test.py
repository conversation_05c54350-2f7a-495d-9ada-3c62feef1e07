#!/usr/bin/env python3
"""
Super quick test - just check if models are initialized with different sizes
Takes < 1 minute
"""

import sys
import torch
sys.path.append('/root/code/GraphLLM4CTR/models')

from expert_fusion_focused import FocusedHybridExpertAdaptor
from llm_rgcn_layer_res import Res<PERSON><PERSON><PERSON>NEncoder
from llm_hgt_layer_res import Res<PERSON><PERSON><PERSON>GTEncoder
from qformer import TextQFormer

def test_model_initialization():
    print("🚀 Super Quick Test: Model Initialization with Different Embedding Sizes")
    print("=" * 70)
    
    embedding_sizes = [32, 128]
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    for embed_size in embedding_sizes:
        print(f"\n📏 Testing embedding size: {embed_size}")
        
        # Calculate expert output dimension
        expert_output_dim = max(32, embed_size // 2)
        
        # Initialize models
        hgt = ResLLMHGTEncoder(
            input_dim=1024,
            hidden_dim=embed_size,
            num_layers=2,
            num_types=2,
            num_heads=min(4, max(1, embed_size // 64)),
            dropout=0.1,
            device=device
        ).to(device)
        
        rgcn = ResLLMRGCNEncoder(
            input_dim=256,
            hidden_dim=embed_size,
            num_relations=2,
            num_layers=2,
            dropout=0.1,
            device=device
        ).to(device)
        
        model = FocusedHybridExpertAdaptor(
            input_dim=embed_size,
            expert_output_dim=expert_output_dim,
            num_shared_experts=3,
            num_user_experts=3,
            num_item_experts=3,
            hidden_dim=expert_output_dim,
            dropout=0.1
        ).to(device)
        
        # Create fresh TextQFormer (this was the bug!)
        text_qformer = TextQFormer(768, 768, 32, 8)
        
        # Count parameters
        hgt_params = sum(p.numel() for p in hgt.parameters())
        rgcn_params = sum(p.numel() for p in rgcn.parameters())
        model_params = sum(p.numel() for p in model.parameters())
        qformer_params = sum(p.numel() for p in text_qformer.parameters())
        total_params = hgt_params + rgcn_params + model_params
        
        print(f"  HGT parameters:     {hgt_params:,}")
        print(f"  RGCN parameters:    {rgcn_params:,}")
        print(f"  Expert parameters:  {model_params:,}")
        print(f"  QFormer parameters: {qformer_params:,}")
        print(f"  Total parameters:   {total_params:,}")
        print(f"  Expert output dim:  {expert_output_dim}")
        
        # Test a simple forward pass
        try:
            batch_size = 4
            user_feat = torch.randn(batch_size, embed_size).to(device)
            item_feat = torch.randn(batch_size, embed_size).to(device)
            
            prob, _, _ = model(user_feat, item_feat)
            print(f"  ✅ Forward pass successful, output shape: {prob.shape}")
            
        except Exception as e:
            print(f"  ❌ Forward pass failed: {e}")
    
    print("\n🔍 Analysis:")
    print("If the parameter counts are different between embedding sizes,")
    print("then the models are being initialized correctly with different sizes!")
    print("\n" + "=" * 70)

if __name__ == "__main__":
    test_model_initialization()
