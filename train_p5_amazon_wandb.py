#!/usr/bin/env python3
"""
P5 training script for Amazon dataset with Wandb logging
"""

import os
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, log_loss, accuracy_score
from tqdm import tqdm
import json
import wandb

def load_amazon_data():
    """Load Amazon dataset"""
    print("📊 Loading Amazon data...")

    train_df = pd.read_csv('/data/datasets/processed_datasets/amazon/train_new.csv')
    val_df = pd.read_csv('/data/datasets/processed_datasets/amazon/val_new.csv')
    test_df = pd.read_csv('/data/datasets/processed_datasets/amazon/test_new.csv')

    print(f"Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
    return train_df, val_df, test_df

class AmazonP5Dataset(Dataset):
    """Dataset for P5-style text-based CTR prediction"""
    def __init__(self, df, max_length=128):
        self.data = df
        self.max_length = max_length

        # Create text prompts and targets
        self.prompts = []
        self.targets = []
        self.labels = []

        for _, row in df.iterrows():
            user_id = row['user_id']
            item_id = row['item_id']
            brand = row['brand_index']
            price_range = row['price_range_index']
            cat1, cat2, cat3 = row['category_1'], row['category_2'], row['category_3']
            label = row['label']

            # Create descriptive text prompt
            prompt = f"User {user_id} considering item {item_id} from brand {brand} in categories {cat1}-{cat2}-{cat3} with price range {price_range}. Will user interact?"
            target = "yes" if label == 1 else "no"

            self.prompts.append(prompt)
            self.targets.append(target)
            self.labels.append(label)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return {
            'prompt': self.prompts[idx],
            'target': self.targets[idx],
            'label': torch.tensor(self.labels[idx], dtype=torch.float32)
        }

class SimpleP5Model(nn.Module):
    """Simplified P5-style model using embeddings instead of full T5"""
    def __init__(self, vocab_size=50000, embedding_dim=128, hidden_dim=256):
        super().__init__()

        # Simple embedding-based approach
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        self.lstm = nn.LSTM(embedding_dim, hidden_dim, batch_first=True, bidirectional=True)
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 1)
        )

        # Simple tokenizer (hash-based)
        self.vocab_size = vocab_size

    def simple_tokenize(self, texts, max_length=64):
        """Simple hash-based tokenization"""
        batch_tokens = []

        for text in texts:
            words = text.lower().split()[:max_length]
            tokens = [hash(word) % self.vocab_size for word in words]

            # Pad or truncate
            if len(tokens) < max_length:
                tokens.extend([0] * (max_length - len(tokens)))
            else:
                tokens = tokens[:max_length]

            batch_tokens.append(tokens)

        return torch.tensor(batch_tokens, dtype=torch.long)

    def forward(self, prompts):
        # Tokenize prompts
        tokens = self.simple_tokenize(prompts)

        # Move tokens to same device as model
        tokens = tokens.to(next(self.parameters()).device)

        # Embed tokens
        embedded = self.embedding(tokens)

        # LSTM encoding
        lstm_out, (hidden, _) = self.lstm(embedded)

        # Use last hidden state
        final_hidden = torch.cat([hidden[0], hidden[1]], dim=1)

        # Classify
        output = self.classifier(final_hidden)
        return output.squeeze()

def train_p5_amazon():
    """Train P5 on Amazon dataset with Wandb logging"""
    print("🚀 Starting P5 training on Amazon dataset...")

    # Initialize Wandb
    wandb.init(
        project="ctr-prediction-Amazon-baselines",
        name="P5_Amazon_simplified",
        config={
            "model": "P5",
            "dataset": "Amazon",
            "epochs": 5,
            "batch_size": 32,
            "learning_rate": 0.001,
            "optimizer": "Adam",
            "loss": "BCEWithLogitsLoss",
            "embedding_dim": 128,
            "hidden_dim": 256,
            "approach": "simplified_text_based"
        }
    )

    # Load data
    train_df, val_df, test_df = load_amazon_data()

    # Create datasets
    train_dataset = AmazonP5Dataset(train_df)
    val_dataset = AmazonP5Dataset(val_df)
    test_dataset = AmazonP5Dataset(test_df)

    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=64)
    test_loader = DataLoader(test_dataset, batch_size=64)

    # Initialize model
    model = SimpleP5Model()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    # Training setup
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.BCEWithLogitsLoss()

    print("Training P5 on Amazon...")

    # Training loop
    for epoch in range(5):
        model.train()
        total_loss = 0

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            prompts = batch['prompt']
            labels = batch['label'].to(device)

            outputs = model(prompts)
            loss = criterion(outputs, labels)

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        avg_loss = total_loss / len(train_loader)
        print(f"Epoch {epoch+1} Loss: {avg_loss:.4f}")

        # Log training loss to Wandb
        wandb.log({
            "epoch": epoch + 1,
            "train_loss": avg_loss
        })

    # Evaluation
    print("Evaluating P5...")
    model.eval()
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            prompts = batch['prompt']
            labels = batch['label'].numpy()

            outputs = model(prompts)
            preds = torch.sigmoid(outputs).cpu().numpy()

            # Handle both single predictions and batch predictions
            if preds.ndim == 0:  # Single prediction (0-d array)
                all_preds.append(preds.item())
                all_labels.append(labels.item())
            else:  # Batch predictions
                all_preds.extend(preds)
                all_labels.extend(labels)

    # Calculate metrics
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)

    # Clip predictions to avoid numerical issues
    all_preds = np.clip(all_preds, 1e-6, 1 - 1e-6)

    auc = roc_auc_score(all_labels, all_preds)
    logloss = log_loss(all_labels, all_preds)
    accuracy = accuracy_score(all_labels, (all_preds >= 0.5).astype(int))

    # Log final metrics to Wandb
    wandb.log({
        "test_auc": auc,
        "test_logloss": logloss,
        "test_accuracy": accuracy
    })

    # Log summary metrics
    wandb.summary.update({
        "final_test_auc": auc,
        "final_test_logloss": logloss,
        "final_test_accuracy": accuracy
    })

    results = {
        'model': 'P5',
        'dataset': 'Amazon',
        'test_auc': float(auc),
        'test_logloss': float(logloss),
        'test_accuracy': float(accuracy)
    }

    print(f"✅ P5 Results:")
    print(f"   Test AUC: {auc:.4f}")
    print(f"   Test Logloss: {logloss:.4f}")
    print(f"   Test Accuracy: {accuracy:.4f}")

    # Save results locally
    with open('p5_amazon_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print(f"📁 Results saved to: p5_amazon_results.json")

    # Finish Wandb run
    wandb.finish()

    return results

def main():
    print("🎯 P5 Training on Amazon Dataset")
    print("=" * 40)

    results = train_p5_amazon()

    print(f"\n🎯 FINAL P5 RESULTS:")
    print(f"AUC: {results['test_auc']:.4f}")
    print(f"Logloss: {results['test_logloss']:.4f}")
    print(f"Accuracy: {results['test_accuracy']:.4f}")

    print(f"\n📊 Compare with other Amazon baselines:")
    print("- LR: AUC ~0.596, Logloss ~0.607")
    print("- DCNv2: AUC ~0.506, Logloss ~0.619")
    print("- GraphPro: AUC ~0.516, Logloss ~0.617")
    print("- AdaGIN: [Your results]")
    print("- UniSRec: [Your results]")
    print(f"- P5: AUC {results['test_auc']:.4f}, Logloss {results['test_logloss']:.4f}")

if __name__ == "__main__":
    main()
