#!/usr/bin/env python3
"""
Quick test to verify embedding size bug is fixed
Uses small data subset and minimal training
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
import argparse

# Add the models directory to path
sys.path.append('/root/code/GraphLLM4CTR/models')

from embedding_loader import EmbeddingLoader
from expert_fusion_focused import FocusedHybridExpertAdaptor
from llm_rgcn_layer_res import ResLLMRGCNEncoder
from llm_hgt_layer_res import ResLLMHGTEncoder
from graph_input_builder import build_rgcn_input, build_hgt_input
from qformer import TextQFormer

class CTRDataset(Dataset):
    def __init__(self, df, dataset_type='amazon'):
        self.user_ids = df["user_id"].tolist()
        if dataset_type == 'movielens':
            item_column = 'movie_id'
        else:
            item_column = 'item_id'
        self.item_ids = df[item_column].tolist()
        self.labels = df["label"].tolist()

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        return {
            "user_id": self.user_ids[idx],
            "item_id": self.item_ids[idx],
            "label": self.labels[idx]
        }

def quick_test_embedding_size(embedding_size, device="cuda", num_samples=1000, num_batches=5):
    """
    Quick test with minimal data and training
    """
    print(f"\n🧪 Testing embedding size: {embedding_size}")
    
    # Paths
    train_path = '/data/datasets/processed_datasets/amazon/train.csv'
    embedding_dir = '/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.5/aligned_embeddings_epoch1'
    item_meta_path = '/data/datasets/processed_datasets/amazon/train.csv'
    qformer_ckpt = '/data/datasets/processed_datasets/amazon/temperature_analysis_FAST/results/temp_0.5/qformer_epoch1.pt'
    
    # Load small subset of data
    train_df = pd.read_csv(train_path)
    train_df = train_df.sample(n=min(num_samples, len(train_df)), random_state=42).reset_index(drop=True)
    
    print(f"📊 Using {len(train_df)} samples")
    
    # Initialize components
    tokenizer = AutoTokenizer.from_pretrained("/root/code/roberta-base")
    text_qformer = TextQFormer(768, 768, 32, 8)  # Fresh instance
    
    # Configure for Amazon dataset
    item_id_col = "item_id"
    title_col = "title"
    
    loader = EmbeddingLoader(embedding_dir, item_meta_path, item_id_col, title_col, text_qformer, qformer_ckpt, tokenizer, device)
    
    # Create data loader
    train_loader = DataLoader(CTRDataset(train_df, 'amazon'), batch_size=64, shuffle=True)
    
    # Calculate expert output dimension
    expert_output_dim = max(32, embedding_size // 2)
    
    # Initialize models with the specified embedding size
    hgt = ResLLMHGTEncoder(
        input_dim=1024,
        hidden_dim=embedding_size,  # Variable embedding size
        num_layers=2,
        num_types=2,
        num_heads=min(4, max(1, embedding_size // 64)),
        dropout=0.1,
        device=device
    ).to(device)
    
    rgcn = ResLLMRGCNEncoder(
        input_dim=256,
        hidden_dim=embedding_size,  # Variable embedding size
        num_relations=2,
        num_layers=2,
        dropout=0.1,
        device=device
    ).to(device)
    
    model = FocusedHybridExpertAdaptor(
        input_dim=embedding_size,  # Match embedding size
        expert_output_dim=expert_output_dim,
        num_shared_experts=3,
        num_user_experts=3,
        num_item_experts=3,
        hidden_dim=expert_output_dim,
        dropout=0.1
    ).to(device)
    
    # Test forward pass with a few batches
    model.train()
    hgt.train()
    rgcn.train()
    
    total_loss = 0
    batch_count = 0
    
    criterion = torch.nn.BCELoss()
    
    for batch_idx, batch in enumerate(train_loader):
        if batch_idx >= num_batches:  # Only test a few batches
            break
            
        user_ids = batch["user_id"]
        item_ids = batch["item_id"]
        labels = torch.tensor(batch["label"], dtype=torch.float32).to(device)
        
        try:
            # Get HGT embeddings
            x_dict, edge_index_hgt, edge_type_hgt, node_type = build_hgt_input(user_ids, item_ids, loader)
            hgt_out = hgt(x_dict, edge_index_hgt, edge_type_hgt, node_type)
            
            # Get RGCN embeddings
            x_rgcn, edge_index_rgcn, edge_type_rgcn = build_rgcn_input(user_ids, item_ids, loader)
            rgcn_out = rgcn(x_rgcn, edge_index_rgcn, edge_type_rgcn)
            
            # Combine embeddings
            user_feat = hgt_out[:len(user_ids)] + rgcn_out[:len(user_ids)]
            item_feat = hgt_out[len(user_ids):] + rgcn_out[len(user_ids):]
            
            # Forward pass
            prob, _, _ = model(user_feat, item_feat)
            loss = criterion(prob.squeeze(), labels)
            
            total_loss += loss.item()
            batch_count += 1
            
            print(f"  Batch {batch_idx+1}: Loss = {loss.item():.4f}")
            
        except Exception as e:
            print(f"  ❌ Error in batch {batch_idx+1}: {e}")
            return None
    
    avg_loss = total_loss / batch_count if batch_count > 0 else float('inf')
    
    # Calculate model size
    all_params = list(model.parameters()) + list(hgt.parameters()) + list(rgcn.parameters())
    model_size = sum(p.numel() for p in all_params)
    
    result = {
        'embedding_size': embedding_size,
        'expert_output_dim': expert_output_dim,
        'avg_loss': avg_loss,
        'model_size': model_size,
        'batches_tested': batch_count
    }
    
    print(f"  ✅ Avg Loss: {avg_loss:.4f}, Model Size: {model_size:,} params")
    return result

def main():
    print("🚀 Quick Test: Embedding Size Bug Fix Verification")
    print("=" * 60)
    
    # Test two different embedding sizes
    embedding_sizes = [32, 128]
    results = []
    
    for embed_size in embedding_sizes:
        result = quick_test_embedding_size(embed_size, num_samples=500, num_batches=3)
        if result:
            results.append(result)
    
    print("\n📊 Quick Test Results:")
    print("-" * 40)
    for r in results:
        print(f"Embedding Size {r['embedding_size']:3d}: Loss = {r['avg_loss']:.4f}, "
              f"Expert Dim = {r['expert_output_dim']:2d}, Model Size = {r['model_size']:,}")
    
    # Check if results are different
    if len(results) >= 2:
        loss_diff = abs(results[0]['avg_loss'] - results[1]['avg_loss'])
        size_diff = abs(results[0]['model_size'] - results[1]['model_size'])
        
        print(f"\n🔍 Analysis:")
        print(f"  Loss difference: {loss_diff:.4f}")
        print(f"  Model size difference: {size_diff:,} parameters")
        
        if loss_diff > 0.001 or size_diff > 1000:
            print("  ✅ GOOD: Different embedding sizes produce different results!")
            print("  🎉 Bug appears to be FIXED!")
        else:
            print("  ❌ BAD: Results are too similar - bug might still exist")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
