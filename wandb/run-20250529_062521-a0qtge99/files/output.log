📊 Loading Amazon data...
Train: 1087075, Val: 195702, Test: 233089
Training P5 on Amazon...
Epoch 1: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 33972/33972 [05:18<00:00, 106.64it/s]
Epoch 1 Loss: 0.6081
Epoch 2: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 33972/33972 [05:18<00:00, 106.68it/s]
Epoch 2 Loss: 0.5757
Epoch 3: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 33972/33972 [05:24<00:00, 104.77it/s]
Epoch 3 Loss: 0.5592
Epoch 4: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 33972/33972 [05:18<00:00, 106.63it/s]
Epoch 4 Loss: 0.5477
Epoch 5: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 33972/33972 [05:23<00:00, 105.00it/s]
Epoch 5 Loss: 0.5368
Evaluating P5...
Evaluating: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████▉| 3642/3643 [00:16<00:00, 227.09it/s]
Traceback (most recent call last):
  File "train_p5_amazon_wandb.py", line 284, in <module>
    main()
  File "train_p5_amazon_wandb.py", line 268, in main
    results = train_p5_amazon()
  File "train_p5_amazon_wandb.py", line 212, in train_p5_amazon
    all_preds.extend(preds)
TypeError: iteration over a 0-d array
Traceback (most recent call last):
  File "train_p5_amazon_wandb.py", line 284, in <module>
    main()
  File "train_p5_amazon_wandb.py", line 268, in main
    results = train_p5_amazon()
  File "train_p5_amazon_wandb.py", line 212, in train_p5_amazon
    all_preds.extend(preds)
TypeError: iteration over a 0-d array
