2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_setup.py:_flush():68] Current SDK version is 0.19.10
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_setup.py:_flush():68] Configure stats pid to 117479
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_setup.py:_flush():68] Loading settings from /root/.config/wandb/settings
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_setup.py:_flush():68] Loading settings from /root/code/wandb/settings
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_setup.py:_flush():68] Loading settings from environment variables
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/wandb/run-20250529_062521-a0qtge99/logs/debug.log
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/wandb/run-20250529_062521-a0qtge99/logs/debug-internal.log
2025-05-29 06:25:21,373 INFO    MainThread:117479 [wandb_init.py:init():852] calling init triggers
2025-05-29 06:25:21,374 INFO    MainThread:117479 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model': 'P5', 'dataset': 'Amazon', 'epochs': 5, 'batch_size': 32, 'learning_rate': 0.001, 'optimizer': 'Adam', 'loss': 'BCEWithLogitsLoss', 'embedding_dim': 128, 'hidden_dim': 256, 'approach': 'simplified_text_based', '_wandb': {}}
2025-05-29 06:25:21,374 INFO    MainThread:117479 [wandb_init.py:init():893] starting backend
2025-05-29 06:25:21,374 INFO    MainThread:117479 [wandb_init.py:init():897] sending inform_init request
2025-05-29 06:25:21,395 INFO    MainThread:117479 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 06:25:21,395 INFO    MainThread:117479 [wandb_init.py:init():907] backend started and connected
2025-05-29 06:25:21,397 INFO    MainThread:117479 [wandb_init.py:init():1002] updated telemetry
2025-05-29 06:25:21,397 INFO    MainThread:117479 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 06:25:22,469 INFO    MainThread:117479 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 06:25:22,591 INFO    MainThread:117479 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 06:25:22,592 INFO    MainThread:117479 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 06:25:22,592 INFO    MainThread:117479 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 06:25:22,592 INFO    MainThread:117479 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 06:25:22,593 INFO    MainThread:117479 [wandb_init.py:init():1147] run started, returning control to user process
