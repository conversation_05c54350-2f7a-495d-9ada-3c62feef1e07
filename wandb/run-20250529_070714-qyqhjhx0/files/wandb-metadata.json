{"os": "Linux-5.4.0-216-generic-x86_64-with-glibc2.17", "python": "CPython 3.8.19", "startedAt": "2025-05-29T07:07:14.745368Z", "program": "train_p5_amazon_wandb.py", "codePath": "train_p5_amazon_wandb.py", "email": "<EMAIL>", "root": "/root/code", "host": "vmInstancekdtfa3ig", "executable": "/root/miniconda3/envs/py38/bin/python", "codePathLocal": "train_p5_amazon_wandb.py", "cpu_count": 16, "cpu_count_logical": 16, "gpu": "NVIDIA GeForce RTX 4090", "gpu_count": 1, "disk": {"/": {"total": "51835101184", "used": "42487304192"}}, "memory": {"total": "120219561984"}, "cpu": {"count": 16, "countLogical": 16}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4090", "memoryTotal": "25757220864", "cudaCores": 16384, "architecture": "Ada"}], "cudaVersion": "12.6"}