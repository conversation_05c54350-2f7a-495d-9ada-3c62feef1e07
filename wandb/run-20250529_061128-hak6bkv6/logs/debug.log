2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /root/code/wandb/run-20250529_061128-hak6bkv6/logs/debug.log
2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /root/code/wandb/run-20250529_061128-hak6bkv6/logs/debug-internal.log
2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:init():852] calling init triggers
2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model': 'UniSRec', 'dataset': 'Amazon', 'epochs': 10, 'batch_size': 256, 'learning_rate': 0.001, 'optimizer': 'Adam', 'loss': 'BCEWithLogitsLoss', '_wandb': {}}
2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:init():893] starting backend
2025-05-29 06:11:28,685 INFO    MainThread:103949 [wandb_init.py:init():897] sending inform_init request
2025-05-29 06:11:28,686 INFO    MainThread:103949 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-05-29 06:11:28,686 INFO    MainThread:103949 [wandb_init.py:init():907] backend started and connected
2025-05-29 06:11:28,687 INFO    MainThread:103949 [wandb_init.py:init():1002] updated telemetry
2025-05-29 06:11:28,688 INFO    MainThread:103949 [wandb_init.py:init():1026] communicating run to backend with 90.0 second timeout
2025-05-29 06:11:30,209 INFO    MainThread:103949 [wandb_init.py:init():1101] starting run threads in backend
2025-05-29 06:11:30,333 INFO    MainThread:103949 [wandb_run.py:_console_start():2566] atexit reg
2025-05-29 06:11:30,333 INFO    MainThread:103949 [wandb_run.py:_redirect():2414] redirect: wrap_raw
2025-05-29 06:11:30,333 INFO    MainThread:103949 [wandb_run.py:_redirect():2483] Wrapping output streams.
2025-05-29 06:11:30,333 INFO    MainThread:103949 [wandb_run.py:_redirect():2506] Redirects installed.
2025-05-29 06:11:30,334 INFO    MainThread:103949 [wandb_init.py:init():1147] run started, returning control to user process
2025-05-29 06:17:40,232 INFO    MainThread:103949 [wandb_run.py:_finish():2314] finishing run 9petrestaurant-huazhong-university-of-science-and-technology/ctr-prediction-Amazon-baselines/hak6bkv6
2025-05-29 06:17:40,232 INFO    MainThread:103949 [wandb_run.py:_atexit_cleanup():2531] got exitcode: 0
2025-05-29 06:17:40,232 INFO    MainThread:103949 [wandb_run.py:_restore():2513] restore
2025-05-29 06:17:40,232 INFO    MainThread:103949 [wandb_run.py:_restore():2519] restore done
2025-05-29 06:17:42,501 INFO    MainThread:103949 [wandb_run.py:_footer_history_summary_info():4160] rendering history
2025-05-29 06:17:42,501 INFO    MainThread:103949 [wandb_run.py:_footer_history_summary_info():4192] rendering summary
2025-05-29 06:17:42,502 INFO    MainThread:103949 [wandb_run.py:_footer_sync_info():4121] logging synced files
